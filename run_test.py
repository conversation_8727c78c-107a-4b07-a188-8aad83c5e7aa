#!/usr/bin/env python3
"""
Test runner for the cattle management application
"""

import sys
import traceback

def test_import():
    """Test importing the main application"""
    try:
        print("Testing imports...")
        
        # Test basic imports
        from flask import Flask
        print("✅ Flask imported successfully")
        
        from flask_sqlalchemy import SQLAlchemy
        print("✅ SQLAlchemy imported successfully")
        
        # Test app import
        from app import app, db
        print("✅ App imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {str(e)}")
        traceback.print_exc()
        return False

def test_database():
    """Test database connection"""
    try:
        from app import app, db
        
        with app.app_context():
            # Try to create tables
            db.create_all()
            print("✅ Database tables created successfully")
            
            return True
            
    except Exception as e:
        print(f"❌ Database error: {str(e)}")
        traceback.print_exc()
        return False

def run_app():
    """Run the application"""
    try:
        from app import app
        
        print("Starting Flask application...")
        print("Access the app at: http://localhost:5000")
        print("Press Ctrl+C to stop")
        
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except Exception as e:
        print(f"❌ Runtime error: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Cattle Management System - Test Runner")
    print("=" * 50)
    
    # Test imports
    if not test_import():
        print("❌ Import test failed")
        sys.exit(1)
    
    # Test database
    if not test_database():
        print("❌ Database test failed")
        sys.exit(1)
    
    print("✅ All tests passed!")
    print("=" * 50)
    
    # Run the app
    run_app()
