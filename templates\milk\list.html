{% extends "base.html" %}

{% block title %}إنتاج الحليب - نظام إدارة الأبقار{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-tint"></i> سجلات إنتاج الحليب</h1>
            <div>
                <!-- عرض سعر الحليب الحالي -->
                <div class="d-inline-block me-3">
                    <small class="text-muted">سعر اللتر:</small>
                    <span class="badge bg-warning text-dark fs-6 milk-price-display">
                        <i class="fas fa-coins"></i> {{ "%.2f"|format(milk_price) }} د.أ
                    </span>
                    <button class="btn btn-sm btn-outline-warning ms-1" data-bs-toggle="modal" data-bs-target="#milkPriceModal" title="تحديث سعر الحليب">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
                <a href="{{ url_for('select_cattle_for_milk') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> تسجيل إنتاج جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الإنتاج -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body text-center">
                <i class="fas fa-tint fa-2x mb-2"></i>
                <h4>{{ records|sum(attribute='total_amount')|round(1) }}</h4>
                <p class="mb-0">إجمالي الإنتاج (لتر)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body text-center">
                <i class="fas fa-calendar-day fa-2x mb-2"></i>
                <h4>{{ (records|sum(attribute='total_amount') / records|length)|round(1) if records else 0 }}</h4>
                <p class="mb-0">متوسط يومي (لتر)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body text-center">
                <i class="fas fa-star fa-2x mb-2"></i>
                <h4>{{ records|selectattr("quality_grade", "equalto", "ممتاز")|list|length }}</h4>
                <p class="mb-0">إنتاج ممتاز</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body text-center">
                <i class="fas fa-coins fa-2x mb-2"></i>
                <h4>{{ "%.0f"|format(total_estimated_value or 0) }}</h4>
                <p class="mb-0">إجمالي القيمة المقدرة (د.أ)</p>
            </div>
        </div>
    </div>
</div>

<!-- فلترة وبحث -->
<div class="row mb-3">
    <div class="col-md-4">
        <select class="form-select" id="cattleFilter">
            <option value="">جميع الأبقار</option>
            {% for record in records %}
                {% if record.cattle.tag_number not in (records|map(attribute='cattle.tag_number')|list)[:loop.index-1] %}
                    <option value="{{ record.cattle.tag_number }}">{{ record.cattle.tag_number }} - {{ record.cattle.name or 'بدون اسم' }}</option>
                {% endif %}
            {% endfor %}
        </select>
    </div>
    <div class="col-md-4">
        <input type="date" class="form-control" id="dateFilter" placeholder="فلترة بالتاريخ">
    </div>
    <div class="col-md-4">
        <select class="form-select" id="qualityFilter">
            <option value="">جميع درجات الجودة</option>
            <option value="ممتاز">ممتاز</option>
            <option value="جيد جداً">جيد جداً</option>
            <option value="جيد">جيد</option>
            <option value="مقبول">مقبول</option>
        </select>
    </div>
</div>

<!-- جدول السجلات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-list"></i> سجلات الإنتاج
            </div>
            <div class="card-body">
                {% if records %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="milkTable">
                        <thead class="table-dark">
                            <tr>
                                <th>التاريخ</th>
                                <th>البقرة</th>
                                <th>الصباح (لتر)</th>
                                <th>المساء (لتر)</th>
                                <th>الإجمالي (لتر)</th>
                                <th>درجة الجودة</th>
                                <th>القيمة المقدرة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in records %}
                            <tr data-cattle="{{ record.cattle.tag_number }}" 
                                data-date="{{ record.date.strftime('%Y-%m-%d') if record.date else '' }}"
                                data-quality="{{ record.quality_grade or '' }}">
                                <td>{{ record.date.strftime('%Y/%m/%d') if record.date else '-' }}</td>
                                <td>
                                    <a href="{{ url_for('cattle_detail', id=record.cattle.id) }}" class="text-decoration-none">
                                        <strong>{{ record.cattle.tag_number }}</strong>
                                        {% if record.cattle.name %}
                                            <br><small class="text-muted">{{ record.cattle.name }}</small>
                                        {% endif %}
                                    </a>
                                </td>
                                <td class="text-end">{{ record.morning_amount }}</td>
                                <td class="text-end">{{ record.evening_amount }}</td>
                                <td class="text-end"><strong>{{ record.total_amount }}</strong></td>
                                <td>
                                    {% if record.quality_grade == 'ممتاز' %}
                                        <span class="badge bg-success">{{ record.quality_grade }}</span>
                                    {% elif record.quality_grade == 'جيد جداً' %}
                                        <span class="badge bg-info">{{ record.quality_grade }}</span>
                                    {% elif record.quality_grade == 'جيد' %}
                                        <span class="badge bg-primary">{{ record.quality_grade }}</span>
                                    {% elif record.quality_grade == 'مقبول' %}
                                        <span class="badge bg-warning">{{ record.quality_grade }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td class="text-end currency">{{ "%.2f"|format(record.estimated_value or (record.total_amount * milk_price)) }} د.أ</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('view_milk_record', record_id=record.id) }}" class="btn btn-sm btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_milk_record', record_id=record.id) }}" class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('delete_milk_record', record_id=record.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا السجل؟')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tint fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد سجلات إنتاج</h4>
                    <p class="text-muted">ابدأ بتسجيل أول إنتاج حليب</p>
                    <a href="{{ url_for('select_cattle_for_milk') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> تسجيل إنتاج جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // فلترة الجدول
    function filterTable() {
        const cattleFilter = document.getElementById('cattleFilter').value;
        const dateFilter = document.getElementById('dateFilter').value;
        const qualityFilter = document.getElementById('qualityFilter').value;
        const rows = document.querySelectorAll('#milkTable tbody tr');
        
        rows.forEach(row => {
            let show = true;
            
            if (cattleFilter && row.dataset.cattle !== cattleFilter) {
                show = false;
            }
            
            if (dateFilter && row.dataset.date !== dateFilter) {
                show = false;
            }
            
            if (qualityFilter && row.dataset.quality !== qualityFilter) {
                show = false;
            }
            
            row.style.display = show ? '' : 'none';
        });
    }
    
    // ربط الفلاتر
    document.getElementById('cattleFilter').addEventListener('change', filterTable);
    document.getElementById('dateFilter').addEventListener('change', filterTable);
    document.getElementById('qualityFilter').addEventListener('change', filterTable);

    // تحديث معاينة التأثير عند تغيير سعر الحليب في Modal
    if (document.getElementById('milk_price_update')) {
        document.getElementById('milk_price_update').addEventListener('input', function() {
            const newPrice = parseFloat(this.value) || 0;
            // يمكن إضافة معاينة هنا إذا لزم الأمر
        });
    }
</script>
{% endblock %}

<!-- Modal تحديث سعر الحليب -->
<div class="modal fade" id="milkPriceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-tint"></i> تحديث سعر الحليب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('update_milk_price') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="current_price_display" class="form-label">السعر الحالي</label>
                        <input type="text" class="form-control" id="current_price_display" value="{{ "%.2f"|format(milk_price) }} د.أ/لتر" readonly>
                    </div>

                    <div class="mb-3">
                        <label for="milk_price" class="form-label">السعر الجديد (د.أ/لتر) *</label>
                        <input type="number" step="0.01" min="0" class="form-control" id="milk_price" name="milk_price" value="{{ milk_price }}" required>
                        <div class="form-text">أدخل السعر الجديد لكل لتر حليب</div>
                    </div>

                    <div class="mb-3">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>تنبيه:</strong> تحديث السعر سيؤثر على حسابات الإيرادات المستقبلية فقط.
                        </div>
                    </div>

                    <!-- معاينة التأثير -->
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-calculator"></i> معاينة التأثير على الإيرادات
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-4">
                                    <small class="text-muted">10 لتر</small>
                                    <div id="preview_10" class="fw-bold">{{ "%.2f"|format(milk_price * 10) }} د.أ</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">50 لتر</small>
                                    <div id="preview_50" class="fw-bold">{{ "%.2f"|format(milk_price * 50) }} د.أ</div>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted">100 لتر</small>
                                    <div id="preview_100" class="fw-bold">{{ "%.2f"|format(milk_price * 100) }} د.أ</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> تحديث السعر
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


