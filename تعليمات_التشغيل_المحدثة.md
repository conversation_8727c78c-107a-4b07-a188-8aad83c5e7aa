# تعليمات تشغيل نظام إدارة الأبقار - النسخة المحدثة

## التحديثات الجديدة ✨

تم تطبيق التحديثات التالية على النظام:

### 1. إضافة حقل القيمة المقدرة
- ✅ تم إضافة عمود `estimated_value` إلى جدول `milk_production`
- ✅ يتم حساب وحفظ القيمة المقدرة تلقائياً عند إضافة أو تعديل سجل الحليب
- ✅ عرض إجمالي القيم المقدرة في صفحة قائمة سجلات الحليب

## طرق التشغيل

### الطريقة 1: التشغيل السريع
```bash
# انقر مرتين على الملف
quick_start.bat
```

### الطريقة 2: من سطر الأوامر
```bash
# افتح Command Prompt أو PowerShell في مجلد المشروع
python simple_start.py
```

### الطريقة 3: التشغيل التقليدي
```bash
python app.py
```

### الطريقة 4: باستخدام ملف run.py
```bash
python run.py
```

## استكشاف الأخطاء

### إذا لم يعمل التطبيق:

1. **تحقق من Python**:
   ```bash
   python --version
   ```
   يجب أن يظهر Python 3.7 أو أحدث

2. **تثبيت المتطلبات**:
   ```bash
   pip install -r requirements.txt
   ```

3. **تشغيل من المجلد الصحيح**:
   تأكد أنك في مجلد المشروع الذي يحتوي على ملف `app.py`

4. **تحقق من المنافذ**:
   تأكد أن المنفذ 5000 غير مستخدم من تطبيق آخر

### رسائل الخطأ الشائعة:

#### "ERR_CONNECTION_REFUSED"
- السبب: التطبيق لم يبدأ بشكل صحيح
- الحل: تحقق من رسائل الخطأ في سطر الأوامر

#### "ModuleNotFoundError"
- السبب: مكتبات Python غير مثبتة
- الحل: `pip install -r requirements.txt`

#### "Database locked"
- السبب: قاعدة البيانات مستخدمة من تطبيق آخر
- الحل: أغلق جميع نسخ التطبيق وأعد التشغيل

## الوصول إلى التطبيق

بعد التشغيل الناجح:
- افتح المتصفح
- اذهب إلى: `http://localhost:5000`
- أو: `http://127.0.0.1:5000`

## اختبار الميزات الجديدة

### 1. اختبار القيمة المقدرة:
1. اذهب إلى "إنتاج الحليب"
2. أضف سجل حليب جديد
3. لاحظ حساب القيمة المقدرة تلقائياً
4. احفظ السجل
5. تحقق من حفظ القيمة في قاعدة البيانات

### 2. مراجعة الإحصائيات:
1. اذهب إلى قائمة سجلات الحليب
2. لاحظ إجمالي القيم المقدرة في الإحصائيات
3. تحقق من عرض القيم الصحيحة لكل سجل

## الملفات المهمة

- `app.py` - التطبيق الرئيسي
- `simple_start.py` - ملف التشغيل المبسط
- `quick_start.bat` - ملف التشغيل السريع
- `add_estimated_value_migration.py` - سكريپت تحديث قاعدة البيانات
- `ESTIMATED_VALUE_UPDATE.md` - تفاصيل التحديثات

## الدعم

إذا واجهت مشاكل:
1. تحقق من ملف `استكشاف_الأخطاء.md`
2. راجع رسائل الخطأ في سطر الأوامر
3. تأكد من تثبيت جميع المتطلبات

## ملاحظات مهمة

- ✅ تم تحديث جميع ملفات التطبيق لدعم القيمة المقدرة
- ✅ التحديثات متوافقة مع السجلات القديمة
- ✅ لا حاجة لحذف البيانات الموجودة
- ✅ يمكن تشغيل سكريپت Migration لتحديث قاعدة البيانات الموجودة
