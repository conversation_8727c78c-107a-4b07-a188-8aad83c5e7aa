#!/usr/bin/env python3
"""
Test script for estimated value functionality
سكريپت اختبار وظيفة القيمة المقدرة
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app, db, MilkProduction, Cattle
    from datetime import date, datetime
    
    def test_estimated_value():
        """اختبار وظيفة القيمة المقدرة"""
        
        with app.app_context():
            try:
                # إنشاء الجداول
                db.create_all()
                print("✅ تم إنشاء الجداول")
                
                # التحقق من وجود بيانات اختبارية
                cattle_count = Cattle.query.count()
                milk_count = MilkProduction.query.count()
                
                print(f"عدد الأبقار: {cattle_count}")
                print(f"عدد سجلات الحليب: {milk_count}")
                
                # إنشاء بقرة اختبارية إذا لم تكن موجودة
                if cattle_count == 0:
                    test_cattle = Cattle(
                        tag_number="TEST001",
                        name="بقرة اختبارية",
                        breed="هولشتاين",
                        gender="أنثى",
                        birth_date=date(2020, 1, 1),
                        status="نشط"
                    )
                    db.session.add(test_cattle)
                    db.session.commit()
                    print("✅ تم إنشاء بقرة اختبارية")
                
                # الحصول على أول بقرة
                cattle = Cattle.query.first()
                if not cattle:
                    print("❌ لا توجد أبقار")
                    return False
                
                # إنشاء سجل حليب اختبار
                test_record = MilkProduction(
                    cattle_id=cattle.id,
                    date=date.today(),
                    morning_amount=10.5,
                    evening_amount=8.3,
                    total_amount=18.8,
                    estimated_value=18.8 * 0.80,  # القيمة المقدرة
                    quality_grade="ممتاز",
                    notes="سجل اختبار للقيمة المقدرة"
                )
                
                db.session.add(test_record)
                db.session.commit()
                
                print("✅ تم إنشاء سجل حليب اختبار")
                print(f"الكمية الإجمالية: {test_record.total_amount} لتر")
                print(f"القيمة المقدرة: {test_record.estimated_value} دينار")
                
                # اختبار استعلام السجلات
                records = MilkProduction.query.all()
                total_estimated = sum(record.estimated_value or 0 for record in records)
                
                print(f"إجمالي القيم المقدرة: {total_estimated} دينار")
                
                return True
                
            except Exception as e:
                print(f"❌ خطأ في الاختبار: {str(e)}")
                return False
    
    if __name__ == "__main__":
        print("بدء اختبار القيمة المقدرة...")
        print("=" * 50)
        
        success = test_estimated_value()
        
        print("=" * 50)
        if success:
            print("✅ نجح الاختبار!")
            print("يمكنك الآن تشغيل التطبيق الرئيسي")
        else:
            print("❌ فشل الاختبار!")

except ImportError as e:
    print(f"❌ خطأ في استيراد الوحدات: {str(e)}")
    print("تأكد من وجود ملف app.py في نفس المجلد")
