#!/usr/bin/env python3
"""
Setup database with estimated_value column
إعداد قاعدة البيانات مع عمود القيمة المقدرة
"""

from app import app, db, MilkProduction
import sqlite3
import os

def setup_database():
    """إعداد قاعدة البيانات مع العمود الجديد"""
    
    with app.app_context():
        try:
            # إنشاء الجداول إذا لم تكن موجودة
            db.create_all()
            print("تم إنشاء الجداول بنجاح")
            
            # التحقق من وجود عمود estimated_value
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('milk_production')]
            
            if 'estimated_value' not in columns:
                print("إضافة عمود estimated_value...")
                
                # استخدام SQL مباشر لإضافة العمود
                db.engine.execute('ALTER TABLE milk_production ADD COLUMN estimated_value REAL DEFAULT 0')
                
                # تحديث السجلات الموجودة
                records = MilkProduction.query.all()
                for record in records:
                    if not record.estimated_value:
                        record.estimated_value = (record.total_amount or 0) * 0.80
                
                db.session.commit()
                print(f"تم تحديث {len(records)} سجل")
            else:
                print("عمود estimated_value موجود بالفعل")
            
            return True
            
        except Exception as e:
            print(f"خطأ في إعداد قاعدة البيانات: {str(e)}")
            return False

if __name__ == "__main__":
    print("بدء إعداد قاعدة البيانات...")
    success = setup_database()
    
    if success:
        print("✅ تم إعداد قاعدة البيانات بنجاح!")
    else:
        print("❌ فشل في إعداد قاعدة البيانات")
