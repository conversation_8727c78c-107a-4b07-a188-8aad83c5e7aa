from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date, timedelta
import os

def calculate_age(birth_date):
    """حساب العمر بالسنوات"""
    if not birth_date:
        return None
    today = date.today()
    age = today.year - birth_date.year
    if today < birth_date.replace(year=today.year):
        age -= 1
    return age

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///cattle_management.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# إضافة دالة مساعدة للقوالب
@app.template_filter('calculate_age')
def calculate_age_filter(birth_date):
    """حساب العمر للاستخدام في القوالب"""
    return calculate_age(birth_date)

# نماذج قاعدة البيانات
class Cattle(db.Model):
    __tablename__ = 'cattle'
    
    id = db.Column(db.Integer, primary_key=True)
    tag_number = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100))
    breed = db.Column(db.String(50))
    gender = db.Column(db.String(10))
    birth_date = db.Column(db.Date)
    weight = db.Column(db.Float)
    color = db.Column(db.String(50))
    status = db.Column(db.String(20), default='نشط')
    purchase_price = db.Column(db.Float)
    purchase_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    health_records = db.relationship('HealthRecord', backref='cattle', lazy=True)
    milk_records = db.relationship('MilkProduction', backref='cattle', lazy=True)
    breeding_records = db.relationship('BreedingRecord', backref='cattle', lazy=True)

class HealthRecord(db.Model):
    __tablename__ = 'health_records'
    
    id = db.Column(db.Integer, primary_key=True)
    cattle_id = db.Column(db.Integer, db.ForeignKey('cattle.id'), nullable=False)
    record_type = db.Column(db.String(50))
    description = db.Column(db.Text)
    date = db.Column(db.Date)
    veterinarian = db.Column(db.String(100))
    cost = db.Column(db.Float)
    next_due_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class MilkProduction(db.Model):
    __tablename__ = 'milk_production'

    id = db.Column(db.Integer, primary_key=True)
    cattle_id = db.Column(db.Integer, db.ForeignKey('cattle.id'), nullable=False)
    date = db.Column(db.Date)
    morning_amount = db.Column(db.Float, default=0)
    evening_amount = db.Column(db.Float, default=0)
    total_amount = db.Column(db.Float)
    estimated_value = db.Column(db.Float, default=0)  # القيمة المقدرة
    quality_grade = db.Column(db.String(20))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class BreedingRecord(db.Model):
    __tablename__ = 'breeding_records'
    
    id = db.Column(db.Integer, primary_key=True)
    cattle_id = db.Column(db.Integer, db.ForeignKey('cattle.id'), nullable=False)
    breeding_date = db.Column(db.Date)
    bull_info = db.Column(db.String(200))
    expected_calving_date = db.Column(db.Date)
    actual_calving_date = db.Column(db.Date)
    pregnancy_status = db.Column(db.String(20))
    calf_info = db.Column(db.Text)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class FinancialRecord(db.Model):
    __tablename__ = 'financial_records'
    
    id = db.Column(db.Integer, primary_key=True)
    transaction_type = db.Column(db.String(20))
    category = db.Column(db.String(50))
    description = db.Column(db.Text)
    amount = db.Column(db.Float)
    date = db.Column(db.Date)
    cattle_id = db.Column(db.Integer, db.ForeignKey('cattle.id'), nullable=True)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# الصفحة الرئيسية
@app.route('/')
def index():
    total_cattle = Cattle.query.count()
    active_cattle = Cattle.query.filter_by(status='نشط').count()
    
    current_month = datetime.now().month
    current_year = datetime.now().year
    monthly_milk = db.session.query(db.func.sum(MilkProduction.total_amount)).filter(
        db.extract('month', MilkProduction.date) == current_month,
        db.extract('year', MilkProduction.date) == current_year
    ).scalar() or 0
    
    monthly_income = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
        FinancialRecord.transaction_type == 'دخل',
        db.extract('month', FinancialRecord.date) == current_month,
        db.extract('year', FinancialRecord.date) == current_year
    ).scalar() or 0
    
    return render_template('index.html', 
                         total_cattle=total_cattle,
                         active_cattle=active_cattle,
                         monthly_milk=monthly_milk,
                         monthly_income=monthly_income)

# إدارة الأبقار
@app.route('/cattle')
def cattle_list():
    cattle = Cattle.query.all()
    return render_template('cattle/list.html', cattle=cattle)

@app.route('/cattle/add', methods=['GET', 'POST'])
def add_cattle():
    if request.method == 'POST':
        cattle = Cattle(
            tag_number=request.form['tag_number'],
            name=request.form['name'],
            breed=request.form['breed'],
            gender=request.form['gender'],
            birth_date=datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date() if request.form['birth_date'] else None,
            weight=float(request.form['weight']) if request.form['weight'] else None,
            color=request.form['color'],
            purchase_price=float(request.form['purchase_price']) if request.form['purchase_price'] else None,
            purchase_date=datetime.strptime(request.form['purchase_date'], '%Y-%m-%d').date() if request.form['purchase_date'] else None,
            notes=request.form['notes']
        )
        
        try:
            db.session.add(cattle)
            db.session.commit()
            flash('تم إضافة البقرة بنجاح!', 'success')
            return redirect(url_for('cattle_list'))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ في إضافة البقرة!', 'error')
    
    return render_template('cattle/add.html')

@app.route('/cattle/<int:id>')
def cattle_detail(id):
    cattle = Cattle.query.get_or_404(id)
    health_records = HealthRecord.query.filter_by(cattle_id=id).order_by(HealthRecord.date.desc()).all()
    milk_records = MilkProduction.query.filter_by(cattle_id=id).order_by(MilkProduction.date.desc()).limit(10).all()
    breeding_records = BreedingRecord.query.filter_by(cattle_id=id).order_by(BreedingRecord.breeding_date.desc()).all()
    
    return render_template('cattle/detail.html', 
                         cattle=cattle,
                         health_records=health_records,
                         milk_records=milk_records,
                         breeding_records=breeding_records)

# السجلات الصحية - الدوال المهمة
@app.route('/health')
def health_records():
    records = HealthRecord.query.join(Cattle).order_by(HealthRecord.date.desc()).all()
    return render_template('health/list.html', records=records)

@app.route('/health/add/<int:cattle_id>', methods=['GET', 'POST'])
def add_health_record(cattle_id):
    cattle = Cattle.query.get_or_404(cattle_id)
    
    if request.method == 'POST':
        record = HealthRecord(
            cattle_id=cattle_id,
            record_type=request.form['record_type'],
            description=request.form['description'],
            date=datetime.strptime(request.form['date'], '%Y-%m-%d').date(),
            veterinarian=request.form['veterinarian'],
            cost=float(request.form['cost']) if request.form['cost'] else 0,
            next_due_date=datetime.strptime(request.form['next_due_date'], '%Y-%m-%d').date() if request.form['next_due_date'] else None,
            notes=request.form['notes']
        )
        
        try:
            db.session.add(record)
            db.session.commit()
            flash('تم إضافة السجل الصحي بنجاح!', 'success')
            return redirect(url_for('cattle_detail', id=cattle_id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ في إضافة السجل!', 'error')
    
    return render_template('health/add.html', cattle=cattle)

# إنتاج الحليب - الدوال المهمة  
@app.route('/milk')
def milk_production():
    records = MilkProduction.query.join(Cattle).order_by(MilkProduction.date.desc()).all()
    return render_template('milk/list.html', records=records)

@app.route('/milk/add/<int:cattle_id>', methods=['GET', 'POST'])
def add_milk_record(cattle_id):
    cattle = Cattle.query.get_or_404(cattle_id)
    
    if request.method == 'POST':
        morning = float(request.form['morning_amount']) if request.form['morning_amount'] else 0
        evening = float(request.form['evening_amount']) if request.form['evening_amount'] else 0
        total_amount = morning + evening
        # افتراض سعر الحليب 0.80 دينار للتر (يمكن تحديثه لاحقاً من الإعدادات)
        estimated_value = total_amount * 0.80

        record = MilkProduction(
            cattle_id=cattle_id,
            date=datetime.strptime(request.form['date'], '%Y-%m-%d').date(),
            morning_amount=morning,
            evening_amount=evening,
            total_amount=total_amount,
            estimated_value=estimated_value,
            quality_grade=request.form['quality_grade'],
            notes=request.form['notes']
        )
        
        try:
            db.session.add(record)
            db.session.commit()
            flash('تم تسجيل إنتاج الحليب بنجاح!', 'success')
            return redirect(url_for('cattle_detail', id=cattle_id))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ في تسجيل الإنتاج!', 'error')
    
    return render_template('milk/add.html', cattle=cattle)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        print("✅ النظام جاهز!")
        print("🔗 الروابط المتاحة:")
        print("   - السجل الصحي: /health/add/<cattle_id>")
        print("   - تسجيل الحليب: /milk/add/<cattle_id>")
    app.run(debug=True, host='0.0.0.0', port=5000)
