#!/usr/bin/env python3
"""
Simple starter for cattle management system
"""

if __name__ == "__main__":
    try:
        print("Starting Cattle Management System...")
        print("=" * 50)
        
        # Import the app
        from app import app, db
        
        # Create database tables
        with app.app_context():
            db.create_all()
            print("Database initialized successfully")
        
        print("Starting Flask server...")
        print("Access the application at: http://localhost:5000")
        print("Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Run the app
        app.run(debug=True, host='127.0.0.1', port=5000)
        
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting application: {str(e)}")
        import traceback
        traceback.print_exc()
