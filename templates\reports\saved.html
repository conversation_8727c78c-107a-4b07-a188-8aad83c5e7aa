{% extends "base.html" %}

{% block title %}التقارير المحفوظة{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-archive"></i> التقارير المحفوظة</h1>
            <div>
                <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#customReportModal">
                    <i class="fas fa-plus"></i> تقرير مخصص
                </button>
                <a href="{{ url_for('financial_reports') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- قائمة التقارير المتاحة -->
<div class="row">
    {% for report in reports %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar"></i> {{ report.name }}
                </h6>
                <span class="badge bg-primary">{{ report.type }}</span>
            </div>
            <div class="card-body">
                <p class="text-muted small">{{ report.description }}</p>
                
                <div class="mb-3">
                    <small class="text-muted">آخر إنشاء:</small>
                    <div class="fw-bold">{{ report.last_generated.strftime('%Y/%m/%d %H:%M') }}</div>
                </div>
            </div>
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <a href="{{ report.url }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> عرض
                    </a>
                    <button class="btn btn-outline-success btn-sm" onclick="generateReport('{{ report.type }}')">
                        <i class="fas fa-sync"></i> تحديث
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="exportReport('{{ report.type }}')">
                        <i class="fas fa-download"></i> تصدير
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- إحصائيات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-tachometer-alt"></i> إحصائيات سريعة
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-file-alt fa-2x text-primary mb-2"></i>
                            <h4>{{ reports|length }}</h4>
                            <small class="text-muted">تقارير متاحة</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-calendar fa-2x text-success mb-2"></i>
                            <h4>{{ "اليوم"|length }}</h4>
                            <small class="text-muted">تقارير اليوم</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-download fa-2x text-info mb-2"></i>
                            <h4>0</h4>
                            <small class="text-muted">تصديرات اليوم</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h4>{{ reports[0].last_generated.strftime('%H:%M') if reports else '--:--' }}</h4>
                            <small class="text-muted">آخر تحديث</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal للتقرير المخصص -->
<div class="modal fade" id="customReportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-chart-line"></i> إنشاء تقرير مخصص</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('export_custom_report') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="report_type" class="form-label">نوع التقرير</label>
                        <select class="form-select" id="report_type" name="report_type" required>
                            <option value="">اختر نوع التقرير</option>
                            <option value="financial">التقرير المالي</option>
                            <option value="milk">تقرير إنتاج الحليب</option>
                            <option value="health">التقرير الصحي</option>
                            <option value="breeding">تقرير التكاثر</option>
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">خيارات التصدير</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_details" name="include_details" checked>
                            <label class="form-check-label" for="include_details">
                                تضمين التفاصيل الكاملة
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_summary" name="include_summary" checked>
                            <label class="form-check-label" for="include_summary">
                                تضمين الملخص الإحصائي
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download"></i> تصدير التقرير
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    function generateReport(type) {
        if (type === 'monthly') {
            window.location.href = "{{ url_for('monthly_report') }}";
        } else if (type === 'dashboard') {
            window.location.href = "{{ url_for('advanced_dashboard') }}";
        }
    }
    
    function exportReport(type) {
        if (type === 'monthly') {
            window.location.href = "{{ url_for('export_monthly_report') }}";
        } else {
            alert('تصدير هذا النوع من التقارير قيد التطوير!');
        }
    }
    
    // تعيين التواريخ الافتراضية
    document.addEventListener('DOMContentLoaded', function() {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        
        document.getElementById('start_date').value = firstDay.toISOString().split('T')[0];
        document.getElementById('end_date').value = today.toISOString().split('T')[0];
    });
</script>
{% endblock %}
