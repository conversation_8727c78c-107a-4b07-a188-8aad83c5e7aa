#!/usr/bin/env python3
"""
Migration script to add estimated_value column to milk_production table
تطبيق إضافة عمود القيمة المقدرة إلى جدول إنتاج الحليب
"""

import sqlite3
import os
from datetime import datetime

def add_estimated_value_column():
    """إضافة عمود القيمة المقدرة إلى جدول إنتاج الحليب"""
    
    # البحث عن ملف قاعدة البيانات
    db_files = ['cattle_management.db', 'instance/cattle_management.db', 'farm.db', 'database.db', 'app.db']
    db_path = None
    
    for db_file in db_files:
        if os.path.exists(db_file):
            db_path = db_file
            break
    
    if not db_path:
        print("لم يتم العثور على ملف قاعدة البيانات")
        return False
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود الجدول
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='milk_production'
        """)
        
        if not cursor.fetchone():
            print("جدول milk_production غير موجود")
            conn.close()
            return False
        
        # التحقق من وجود العمود
        cursor.execute("PRAGMA table_info(milk_production)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'estimated_value' in columns:
            print("عمود estimated_value موجود بالفعل")
            conn.close()
            return True
        
        # إضافة العمود الجديد
        cursor.execute("""
            ALTER TABLE milk_production 
            ADD COLUMN estimated_value REAL DEFAULT 0
        """)
        
        # تحديث القيم الموجودة بناءً على الكمية الإجمالية وسعر افتراضي
        # سعر افتراضي 0.80 دينار للتر
        cursor.execute("""
            UPDATE milk_production 
            SET estimated_value = total_amount * 0.80 
            WHERE estimated_value IS NULL OR estimated_value = 0
        """)
        
        # حفظ التغييرات
        conn.commit()
        
        print(f"تم إضافة عمود estimated_value بنجاح إلى جدول milk_production")
        print(f"تم تحديث {cursor.rowcount} سجل بالقيم المقدرة")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"خطأ في إضافة العمود: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return False

def main():
    """الدالة الرئيسية"""
    print("بدء عملية إضافة عمود القيمة المقدرة...")
    print("=" * 50)
    
    success = add_estimated_value_column()
    
    if success:
        print("=" * 50)
        print("تمت العملية بنجاح! ✅")
        print("يمكنك الآن تشغيل التطبيق لرؤية القيم المقدرة محفوظة في قاعدة البيانات")
    else:
        print("=" * 50)
        print("فشلت العملية! ❌")
        print("تحقق من وجود ملف قاعدة البيانات وصحة الجدول")

if __name__ == "__main__":
    main()
