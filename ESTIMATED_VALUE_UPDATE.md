# تحديث القيمة المقدرة في صفحة تسجيل الحليب

## التحديثات المطبقة

### 1. إضافة عمود القيمة المقدرة إلى قاعدة البيانات
- تم إضافة عمود `estimated_value` إلى جدول `milk_production`
- العمود من نوع `REAL` مع قيمة افتراضية `0`

### 2. تحديث النماذج (Models)
تم تحديث الملفات التالية:
- `app.py` - إضافة `estimated_value = db.Column(db.Float, default=0)`
- `app_simple.py` - إضافة نفس العمود
- `app_fixed.py` - إضافة نفس العمود

### 3. تحديث دوال حفظ البيانات
- **دالة إضافة سجل جديد**: تحسب القيمة المقدرة تلقائياً عند الحفظ
- **دالة تعديل سجل موجود**: تعيد حساب القيمة المقدرة عند التحديث
- الحساب: `القيمة المقدرة = الكمية الإجمالية × سعر اللتر`

### 4. تحديث واجهات العرض
- **قائمة سجلات الحليب**: عرض القيمة المقدرة المحفوظة من قاعدة البيانات
- **تفاصيل السجل**: عرض القيمة المقدرة الصحيحة
- **الإحصائيات**: إجمالي القيم المقدرة لجميع السجلات

## كيفية تطبيق التحديث

### الخطوة 1: تشغيل Migration
```bash
python add_estimated_value_migration.py
```

### الخطوة 2: إعادة تشغيل التطبيق
```bash
python app.py
```

## الميزات الجديدة

### 1. حفظ القيمة المقدرة
- القيمة المقدرة تُحفظ الآن في قاعدة البيانات
- لا تعتمد على حسابات JavaScript فقط
- تبقى محفوظة حتى لو تغير سعر الحليب لاحقاً

### 2. إحصائيات محسنة
- إجمالي القيم المقدرة لجميع السجلات
- حسابات دقيقة بناءً على البيانات المحفوظة

### 3. التوافق مع السجلات القديمة
- السجلات القديمة تحصل على قيم مقدرة محسوبة تلقائياً
- لا فقدان للبيانات الموجودة

## الملفات المحدثة

### ملفات الخادم
- `app.py` - الملف الرئيسي
- `app_simple.py` - النسخة المبسطة  
- `app_fixed.py` - النسخة المحدثة

### ملفات القوالب
- `templates/milk/list.html` - قائمة السجلات
- `templates/milk/view.html` - عرض تفاصيل السجل

### ملفات جديدة
- `add_estimated_value_migration.py` - سكريبت التحديث
- `ESTIMATED_VALUE_UPDATE.md` - هذا الملف

## ملاحظات مهمة

1. **النسخ الاحتياطية**: يُنصح بعمل نسخة احتياطية من قاعدة البيانات قبل تشغيل Migration
2. **سعر الحليب**: يتم استخدام سعر الحليب الحالي من الإعدادات لحساب القيمة المقدرة
3. **السجلات القديمة**: ستحصل على قيم مقدرة بناءً على سعر افتراضي 0.80 دينار للتر

## اختبار التحديث

1. أضف سجل حليب جديد
2. تحقق من حفظ القيمة المقدرة في قاعدة البيانات
3. راجع الإحصائيات في صفحة قائمة السجلات
4. تأكد من عرض القيم الصحيحة في تفاصيل السجل
