from flask import Flask, render_template, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date, timedelta

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///cattle_management.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# نماذج قاعدة البيانات
class Cattle(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    tag_number = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100))
    breed = db.Column(db.String(50))
    gender = db.Column(db.String(10))
    birth_date = db.Column(db.Date)
    weight = db.Column(db.Float)
    color = db.Column(db.String(50))
    status = db.Column(db.String(20), default='نشط')
    purchase_price = db.Column(db.Float)
    purchase_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class HealthRecord(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    cattle_id = db.Column(db.Integer, db.ForeignKey('cattle.id'), nullable=False)
    record_type = db.Column(db.String(50))
    description = db.Column(db.Text)
    date = db.Column(db.Date)
    veterinarian = db.Column(db.String(100))
    cost = db.Column(db.Float)
    next_due_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    cattle = db.relationship('Cattle', backref='health_records')

class MilkProduction(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    cattle_id = db.Column(db.Integer, db.ForeignKey('cattle.id'), nullable=False)
    date = db.Column(db.Date)
    morning_amount = db.Column(db.Float, default=0)
    evening_amount = db.Column(db.Float, default=0)
    total_amount = db.Column(db.Float)
    estimated_value = db.Column(db.Float, default=0)  # القيمة المقدرة
    quality_grade = db.Column(db.String(20))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    cattle = db.relationship('Cattle', backref='milk_records')

# الصفحة الرئيسية
@app.route('/')
def index():
    total_cattle = Cattle.query.count()
    active_cattle = Cattle.query.filter_by(status='نشط').count()
    return render_template('index.html', total_cattle=total_cattle, active_cattle=active_cattle, monthly_milk=0, monthly_income=0)

# إدارة الأبقار
@app.route('/cattle')
def cattle_list():
    cattle = Cattle.query.all()
    return render_template('cattle/list.html', cattle=cattle)

@app.route('/cattle/add', methods=['GET', 'POST'])
def add_cattle():
    if request.method == 'POST':
        cattle = Cattle(
            tag_number=request.form['tag_number'],
            name=request.form['name'],
            breed=request.form['breed'],
            gender=request.form['gender'],
            birth_date=datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date() if request.form['birth_date'] else None,
            weight=float(request.form['weight']) if request.form['weight'] else None,
            color=request.form['color'],
            purchase_price=float(request.form['purchase_price']) if request.form['purchase_price'] else None,
            purchase_date=datetime.strptime(request.form['purchase_date'], '%Y-%m-%d').date() if request.form['purchase_date'] else None,
            notes=request.form['notes']
        )
        
        try:
            db.session.add(cattle)
            db.session.commit()
            flash('تم إضافة البقرة بنجاح!', 'success')
            return redirect(url_for('cattle_list'))
        except Exception:
            db.session.rollback()
            flash('حدث خطأ في إضافة البقرة!', 'error')
    
    return render_template('cattle/add.html')

@app.route('/cattle/<int:id>')
def cattle_detail(id):
    cattle = Cattle.query.get_or_404(id)
    return render_template('cattle/detail.html', cattle=cattle, health_records=[], milk_records=[])

# السجلات الصحية
@app.route('/health/add/<int:cattle_id>', methods=['GET', 'POST'])
def add_health_record(cattle_id):
    cattle = Cattle.query.get_or_404(cattle_id)
    
    if request.method == 'POST':
        record = HealthRecord(
            cattle_id=cattle_id,
            record_type=request.form['record_type'],
            description=request.form['description'],
            date=datetime.strptime(request.form['date'], '%Y-%m-%d').date(),
            veterinarian=request.form.get('veterinarian', ''),
            cost=float(request.form['cost']) if request.form.get('cost') else 0,
            next_due_date=datetime.strptime(request.form['next_due_date'], '%Y-%m-%d').date() if request.form.get('next_due_date') else None,
            notes=request.form.get('notes', '')
        )
        
        try:
            db.session.add(record)
            db.session.commit()
            flash('تم إضافة السجل الصحي بنجاح!', 'success')
            return redirect(url_for('cattle_detail', id=cattle_id))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في إضافة السجل: {str(e)}', 'error')
    
    return render_template('health/add.html', cattle=cattle)

# إنتاج الحليب
@app.route('/milk/add/<int:cattle_id>', methods=['GET', 'POST'])
def add_milk_record(cattle_id):
    cattle = Cattle.query.get_or_404(cattle_id)
    
    if request.method == 'POST':
        morning = float(request.form['morning_amount']) if request.form.get('morning_amount') else 0
        evening = float(request.form['evening_amount']) if request.form.get('evening_amount') else 0
        total_amount = morning + evening
        # افتراض سعر الحليب 0.80 دينار للتر (يمكن تحديثه لاحقاً من الإعدادات)
        estimated_value = total_amount * 0.80

        record = MilkProduction(
            cattle_id=cattle_id,
            date=datetime.strptime(request.form['date'], '%Y-%m-%d').date(),
            morning_amount=morning,
            evening_amount=evening,
            total_amount=total_amount,
            estimated_value=estimated_value,
            quality_grade=request.form.get('quality_grade', ''),
            notes=request.form.get('notes', '')
        )
        
        try:
            db.session.add(record)
            db.session.commit()
            flash('تم تسجيل إنتاج الحليب بنجاح!', 'success')
            return redirect(url_for('cattle_detail', id=cattle_id))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في تسجيل الإنتاج: {str(e)}', 'error')
    
    return render_template('milk/add.html', cattle=cattle)

# اختبار الروابط
@app.route('/test')
def test_routes():
    return """
    <h1>اختبار الروابط</h1>
    <p><a href="/health/add/1">اختبار السجل الصحي</a></p>
    <p><a href="/milk/add/1">اختبار تسجيل الحليب</a></p>
    <p><a href="/cattle">قائمة الأبقار</a></p>
    """

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        print("النظام جاهز!")
        print("الروابط:")
        for rule in app.url_map.iter_rules():
            if 'add' in rule.endpoint:
                print(f"  {rule.endpoint}: {rule.rule}")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
