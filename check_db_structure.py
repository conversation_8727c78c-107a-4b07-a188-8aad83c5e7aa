import sqlite3
import os

# التحقق من ملفات قاعدة البيانات الموجودة
db_files = ['cattle_management.db', 'instance/cattle_management.db']

for db_file in db_files:
    if os.path.exists(db_file):
        print(f"Found database: {db_file}")
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # التحقق من الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"Tables: {[table[0] for table in tables]}")
            
            # التحقق من بنية جدول milk_production
            if any('milk_production' in table for table in tables):
                cursor.execute("PRAGMA table_info(milk_production)")
                columns = cursor.fetchall()
                print(f"milk_production columns: {[col[1] for col in columns]}")
            
            conn.close()
            print("-" * 40)
        except Exception as e:
            print(f"Error with {db_file}: {e}")
    else:
        print(f"Database not found: {db_file}")
